# Git配置说明 - uni-app微信小程序项目

## 📁 .gitignore 文件配置

已为您的uni-app微信小程序项目创建了完整的.gitignore文件，包含以下配置：

### 🚫 被忽略的文件/目录

#### 1. uni-app编译产物
- `unpackage/` - uni-app编译生成的主要目录
- `dist/` - 构建输出目录
- `build/` - 构建临时目录
- `platforms/` - 各平台编译产物

#### 2. Node.js依赖包
- `node_modules/` - npm/yarn安装的依赖包
- `npm-debug.log*` - npm调试日志
- `yarn-debug.log*` - yarn调试日志
- `pnpm-debug.log*` - pnpm调试日志

#### 3. IDE配置文件
- `.hbuilderx/` - HBuilderX配置目录
- `.vscode/` - VSCode配置（部分保留）
- `.idea/` - WebStorm/IntelliJ配置
- `project.config.json` - 微信开发者工具配置
- `project.private.config.json` - 微信开发者工具私有配置

#### 4. 系统文件
- `.DS_Store` - macOS系统文件
- `Thumbs.db` - Windows缩略图缓存
- `Desktop.ini` - Windows桌面配置

#### 5. 日志文件
- `logs/` - 日志目录
- `*.log` - 各种日志文件

#### 6. 环境配置
- `.env*` - 环境变量文件
- `config.local.js` - 本地配置文件

#### 7. 临时文件
- `tmp/`, `temp/` - 临时目录
- `*.swp`, `*.swo` - 编辑器临时文件
- `*.bak` - 备份文件

### ✅ 被保留的重要文件

以下文件会被提交到Git仓库：

#### 核心配置文件
- `manifest.json` - uni-app应用配置
- `pages.json` - 页面路由配置
- `App.vue` - 应用入口文件
- `main.js` - 主入口文件
- `uni.scss` - 全局样式变量
- `package.json` - 项目依赖配置

#### 源代码文件
- `pages/` - 页面源代码
- `components/` - 组件源代码
- `static/` - 静态资源（图片、字体等）

#### 文档文件
- `README.md` - 项目说明文档
- 其他.md文档文件

## 🚀 Git使用建议

### 初始化Git仓库
```bash
# 在项目根目录执行
git init
git add .
git commit -m "初始化uni-app微信小程序项目"
```

### 常用Git命令
```bash
# 查看文件状态
git status

# 添加文件到暂存区
git add .

# 提交更改
git commit -m "描述你的更改"

# 查看提交历史
git log --oneline

# 创建分支
git checkout -b feature/新功能

# 切换分支
git checkout main
```

### 分支管理建议
```
main/master - 主分支（生产环境）
develop - 开发分支
feature/* - 功能分支
hotfix/* - 热修复分支
```

## ⚠️ 注意事项

### 1. 敏感信息保护
- 不要提交包含API密钥、密码等敏感信息的文件
- 使用环境变量文件(.env)存储敏感配置
- 确保.env文件已被.gitignore忽略

### 2. 大文件处理
- 避免提交大型图片、视频文件
- 考虑使用Git LFS处理大文件
- 优化图片资源大小

### 3. 编译产物
- 永远不要提交编译产物到仓库
- 确保unpackage/目录被忽略
- 在CI/CD中重新构建项目

### 4. 团队协作
- 统一代码格式化规则
- 使用有意义的提交信息
- 定期同步远程仓库

## 🔧 自定义配置

如果需要修改.gitignore规则：

### 添加忽略规则
```bash
# 忽略特定文件
echo "特定文件.txt" >> .gitignore

# 忽略特定目录
echo "特定目录/" >> .gitignore
```

### 取消忽略规则
```bash
# 在.gitignore中添加感叹号
!重要文件.txt
```

### 清除已跟踪的文件
```bash
# 如果文件已被Git跟踪，需要先移除跟踪
git rm --cached 文件名
git commit -m "移除文件跟踪"
```

## 📞 技术支持

如果遇到Git相关问题：
1. 检查.gitignore配置是否正确
2. 确认文件是否已被Git跟踪
3. 使用`git status`查看当前状态
4. 参考Git官方文档

---

**配置时间**: 2025-07-30  
**适用项目**: uni-app微信小程序  
**Git版本**: 兼容所有Git版本
