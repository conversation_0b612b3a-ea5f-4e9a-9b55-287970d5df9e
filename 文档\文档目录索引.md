# 📚 项目文档目录索引

## 文档结构概览

```
文档/
├── 项目说明/                    # 项目介绍和说明文档
│   ├── README.md               # 项目主要说明文档
│   ├── 项目验证报告.md          # 项目验证和测试报告
│   └── 验证清单.md             # 项目验证清单
├── 开发规范/                    # 开发相关规范和指南
│   ├── Git配置说明.md          # Git配置和开发环境说明
│   ├── WXSS兼容性修复报告.md   # 技术问题修复记录
│   ├── Issue模板.md            # Issue提交模板
│   └── PR模板.md               # Pull Request模板
├── 设计资源/                    # 设计和资源相关文档
│   ├── 图标使用指南.md          # 图标使用规范和指南
│   ├── 图片资源修复报告.md      # 图片资源相关问题修复
│   └── 静态资源说明.md          # 静态资源使用说明
└── 部署运维/                    # 部署和运维相关文档
    └── (待添加)
```

## 📋 文档分类说明

### 🏠 项目说明/
存放项目介绍、功能概述、验证报告等项目整体相关的文档。

**主要文档：**
- **README.md** - 项目的主要说明文档，包含项目信息、结构、功能介绍等
- **项目验证报告.md** - TabBar图标问题解决和项目验证报告
- **验证清单.md** - 图片资源修复验证清单

### 🛠️ 开发规范/
存放代码规范、开发环境配置、技术问题解决方案等开发相关文档。

**主要文档：**
- **Git配置说明.md** - Git配置和.gitignore文件说明
- **WXSS兼容性修复报告.md** - 微信小程序WXSS兼容性问题修复记录
- **Issue模板.md** - 项目Issue提交模板
- **PR模板.md** - Pull Request提交模板

### 🎨 设计资源/
存放UI设计规范、图标使用指南、静态资源说明等设计相关文档。

**主要文档：**
- **图标使用指南.md** - 详细的图标文件管理和使用规范
- **图片资源修复报告.md** - 图片资源加载错误修复记录
- **静态资源说明.md** - TabBar图标和静态资源使用说明

### 🚀 部署运维/
存放部署配置、运维手册、环境配置等运维相关文档。

**状态：** 目录已创建，待添加相关文档

## 📖 快速导航

### 新手入门
1. 首先阅读：[项目说明/README.md](./项目说明/README.md)
2. 环境配置：[开发规范/Git配置说明.md](./开发规范/Git配置说明.md)
3. 资源使用：[设计资源/图标使用指南.md](./设计资源/图标使用指南.md)

### 开发参考
- **图标使用**：[设计资源/图标使用指南.md](./设计资源/图标使用指南.md)
- **静态资源**：[设计资源/静态资源说明.md](./设计资源/静态资源说明.md)
- **问题修复**：[开发规范/WXSS兼容性修复报告.md](./开发规范/WXSS兼容性修复报告.md)

### 项目维护
- **验证清单**：[项目说明/验证清单.md](./项目说明/验证清单.md)
- **验证报告**：[项目说明/项目验证报告.md](./项目说明/项目验证报告.md)
- **资源修复**：[设计资源/图片资源修复报告.md](./设计资源/图片资源修复报告.md)

## 🔄 文档迁移记录

### 迁移完成的文档

| 原位置 | 新位置 | 迁移时间 | 状态 |
|--------|--------|----------|------|
| `README.md` | `文档/项目说明/README.md` | 2025-07-31 | ✅ 已完成 |
| `Git配置说明.md` | `文档/开发规范/Git配置说明.md` | 2025-07-31 | ✅ 已完成 |
| `WXSS兼容性修复报告.md` | `文档/开发规范/WXSS兼容性修复报告.md` | 2025-07-31 | ✅ 已完成 |
| `图片资源修复报告.md` | `文档/设计资源/图片资源修复报告.md` | 2025-07-31 | ✅ 已完成 |
| `项目验证报告.md` | `文档/项目说明/项目验证报告.md` | 2025-07-31 | ✅ 已完成 |
| `验证清单.md` | `文档/项目说明/验证清单.md` | 2025-07-31 | ✅ 已完成 |
| `static/images/README.md` | `文档/设计资源/静态资源说明.md` | 2025-07-31 | ✅ 已完成 |
| `.gitee/ISSUE_TEMPLATE.zh-CN.md` | `文档/开发规范/Issue模板.md` | 2025-07-31 | ✅ 已复制 |
| `.gitee/PULL_REQUEST_TEMPLATE.zh-CN.md` | `文档/开发规范/PR模板.md` | 2025-07-31 | ✅ 已复制 |

### 保持原位置的文档
- `.gitee/ISSUE_TEMPLATE.zh-CN.md` - Gitee平台需要，保持原位置
- `.gitee/PULL_REQUEST_TEMPLATE.zh-CN.md` - Gitee平台需要，保持原位置

## 📝 文档维护指南

### 添加新文档
1. 根据文档内容选择合适的分类目录
2. 使用中文文件名，具有描述性
3. 更新本索引文档
4. 确保文档格式统一（Markdown格式）

### 文档命名规范
- 使用中文命名，便于理解
- 文件名要具有描述性
- 使用.md扩展名
- 避免特殊字符和空格

### 文档内容要求
- 使用Markdown格式
- 包含清晰的标题层级
- 提供代码示例（如适用）
- 保持内容更新和准确性

---

**索引版本**：v1.0  
**最后更新**：2025-07-31  
**维护人员**：开发团队
