<template>
	<view class="container">
		<!-- 自定义头部 -->
		<view class="custom-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="header-content">
				<view class="header-left"></view>
				<view class="header-center">
					<text class="title">热量规划</text>
				</view>
				<view class="header-right">
					<image class="icon" src="/static/images/<EMAIL>" mode="aspectFit"></image>
					<image class="icon" src="/static/images/help.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="page-content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
			<!-- 每日热量计划 -->
			<view class="plan-section">
			<view class="section-header">
				<text class="section-title">每日热量计划</text>
				<view class="calculate-btn">
					<text class="btn-text">为我计算</text>
				</view>
			</view>
			
			<view class="calorie-display">
				<text class="calorie-number">1469</text>
				<text class="calorie-unit">千卡/天</text>
			</view>
			
			<view class="progress-container">
				<view class="progress-bar">
					<view class="progress-fill"></view>
				</view>
				<text class="progress-text">每日缺口：367千卡</text>
			</view>
		</view>
		
		<!-- 基础代谢信息 -->
		<view class="metabolism-section">
			<text class="metabolism-title">您的日常基础代谢：1836千卡/天</text>
			<view class="user-info">
				<text class="info-item">身高：175cm</text>
				<text class="info-item">体重：70kg</text>
				<text class="info-item">年龄：26</text>
				<text class="info-item">性别：男</text>
			</view>
		</view>
		
		<!-- 减重模式选择 -->
		<view class="mode-section">
			<text class="section-title">减重模式选择</text>
			<text class="section-subtitle">(修改热量缺口)</text>
			
			<view class="mode-options">
				<view class="mode-option">
					<text class="mode-name">快速减重</text>
					<text class="mode-desc">-1.25kg/周</text>
				</view>
				<view class="mode-option active">
					<text class="mode-name">平稳减重</text>
					<text class="mode-desc">-0.83kg/周</text>
				</view>
				<view class="mode-option">
					<text class="mode-name">体重维持</text>
					<text class="mode-desc">-0kg/周</text>
				</view>
				<view class="mode-option">
					<text class="mode-name">自定义</text>
					<text class="mode-desc">-0kg/周</text>
				</view>
			</view>
		</view>
		
		<!-- 体重预测 -->
		<view class="prediction-section">
			<text class="section-title">体重预测</text>
			
			<view class="chart-container">
				<view class="chart">
					<view class="chart-bar" style="height: 80%;">
						<text class="bar-value">70.0kg</text>
						<text class="bar-label">当前</text>
					</view>
					<view class="chart-bar" style="height: 70%;">
						<text class="bar-value">68.8kg</text>
						<text class="bar-label">1周</text>
					</view>
					<view class="chart-bar" style="height: 60%;">
						<text class="bar-value">65.0kg</text>
						<text class="bar-label">4周</text>
					</view>
					<view class="chart-bar" style="height: 45%;">
						<text class="bar-value">55.0kg</text>
						<text class="bar-label">12周</text>
					</view>
				</view>
			</view>
		</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0
			}
		},
		onLoad() {
			this.getSystemInfo()
		},
		methods: {
			getSystemInfo() {
				uni.getSystemInfo({
					success: (res) => {
						this.statusBarHeight = res.statusBarHeight || 20
					}
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 120rpx;
	}

	/* 自定义头部 */
	.custom-header {
		background-color: #f8f9fa;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
	}

	.header-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;
		position: relative;
	}

	.header-left {
		min-width: 120rpx;
	}

	.header-center {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
	}

	.header-right {
		display: flex;
		align-items: center;
		min-width: 120rpx;
		justify-content: flex-end;
	}

	.icon {
		width: 48rpx;
		height: 48rpx;
		margin-left: 24rpx;
	}
	
	/* 计划部分 */
	.plan-section {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 30rpx;
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.calculate-btn {
		background-color: #E8F8F5;
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		border: 2rpx solid #00D4AA;
	}
	
	.btn-text {
		font-size: 24rpx;
		color: #00D4AA;
	}
	
	.calorie-display {
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.calorie-number {
		font-size: 100rpx;
		font-weight: bold;
		color: #00D4AA;
		display: block;
	}
	
	.calorie-unit {
		font-size: 28rpx;
		color: #666666;
		margin-top: 10rpx;
		display: block;
	}
	
	.progress-container {
		text-align: center;
	}
	
	.progress-bar {
		width: 100%;
		height: 8rpx;
		background-color: #E5E5E5;
		border-radius: 4rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
	}
	
	.progress-fill {
		width: 75%;
		height: 100%;
		background-color: #00D4AA;
	}
	
	.progress-text {
		font-size: 28rpx;
		color: #00D4AA;
	}
	
	/* 基础代谢部分 */
	.metabolism-section {
		background-color: #00D4AA;
		margin-top: 20rpx;
		padding: 30rpx;
		border-radius: 12rpx;
		margin-left: 30rpx;
		margin-right: 30rpx;
	}
	
	.metabolism-title {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
		display: block;
		margin-bottom: 20rpx;
	}
	
	.user-info {
		display: flex;
		justify-content: space-between;
	}
	
	.info-item {
		font-size: 28rpx;
		color: #ffffff;
	}
	
	/* 模式选择部分 */
	.mode-section {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 30rpx;
	}
	
	.section-subtitle {
		font-size: 24rpx;
		color: #999999;
		margin-top: 10rpx;
		display: block;
	}
	
	.mode-options {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
		gap: 20rpx;
	}
	
	.mode-option {
		flex: 1;
		background-color: #f8f8f8;
		padding: 20rpx;
		border-radius: 12rpx;
		text-align: center;
		border: 2rpx solid transparent;
	}
	
	.mode-option.active {
		background-color: #E8F8F5;
		border-color: #00D4AA;
	}
	
	.mode-name {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.mode-desc {
		font-size: 24rpx;
		color: #666666;
	}
	
	/* 体重预测部分 */
	.prediction-section {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 30rpx;
	}
	
	.chart-container {
		margin-top: 30rpx;
	}
	
	.chart {
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		height: 400rpx;
		padding: 20rpx 0;
	}
	
	.chart-bar {
		flex: 1;
		background-color: #4A90E2;
		margin: 0 10rpx;
		border-radius: 8rpx 8rpx 0 0;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		align-items: center;
		padding: 20rpx 10rpx;
		position: relative;
	}
	
	.bar-value {
		font-size: 24rpx;
		color: #ffffff;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.bar-label {
		font-size: 24rpx;
		color: #333333;
		position: absolute;
		bottom: -40rpx;
	}
</style>
